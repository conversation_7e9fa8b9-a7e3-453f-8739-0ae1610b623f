Cannot load Xdebug - it was already loaded
[Wed May 28 12:34:33 2025] PHP 8.1.2-1ubuntu2.21 Development Server (http://localhost:8000) started
[Wed May 28 12:34:38 2025] 127.0.0.1:44492 Accepted
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] 127.0.0.1:44492 [200]: GET /
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] 127.0.0.1:44492 Closing
[Wed May 28 12:34:38 2025] 127.0.0.1:44494 Accepted
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] 127.0.0.1:44494 [200]: GET /doc
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:34:38 2025] 127.0.0.1:44494 Closing
[Wed May 28 12:34:38 2025] 127.0.0.1:44498 Accepted
[Wed May 28 12:34:38 2025] 127.0.0.1:44498 [404]: GET /doc.json - No such file or directory
[Wed May 28 12:34:38 2025] 127.0.0.1:44498 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37572 Accepted
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] 127.0.0.1:37572 [200]: GET /doc
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:05 2025] 127.0.0.1:37572 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37586 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37590 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37600 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37602 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37612 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37586 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui.css
[Wed May 28 12:35:05 2025] 127.0.0.1:37586 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37616 Accepted
[Wed May 28 12:35:05 2025] 127.0.0.1:37590 [200]: GET /bundles/nelmioapidoc/style.css
[Wed May 28 12:35:05 2025] 127.0.0.1:37590 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37600 [200]: GET /bundles/nelmioapidoc/logo.png
[Wed May 28 12:35:05 2025] 127.0.0.1:37600 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37602 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui-bundle.js
[Wed May 28 12:35:05 2025] 127.0.0.1:37612 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui-standalone-preset.js
[Wed May 28 12:35:05 2025] 127.0.0.1:37616 [200]: GET /bundles/nelmioapidoc/init-swagger-ui.js
[Wed May 28 12:35:05 2025] 127.0.0.1:37616 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37612 Closing
[Wed May 28 12:35:05 2025] 127.0.0.1:37602 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54068 Accepted
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] 127.0.0.1:54068 [200]: GET /doc
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] Xdebug: [Step Debug] Could not connect to debugging client. Tried: 127.0.0.1:9003 (through xdebug.client_host/xdebug.client_port) :-(
[Wed May 28 12:35:14 2025] 127.0.0.1:54068 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54080 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54094 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54102 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54104 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54110 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54080 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui.css
[Wed May 28 12:35:14 2025] 127.0.0.1:54080 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54118 Accepted
[Wed May 28 12:35:14 2025] 127.0.0.1:54094 [200]: GET /bundles/nelmioapidoc/style.css
[Wed May 28 12:35:14 2025] 127.0.0.1:54102 [200]: GET /bundles/nelmioapidoc/logo.png
[Wed May 28 12:35:14 2025] 127.0.0.1:54104 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui-bundle.js
[Wed May 28 12:35:14 2025] 127.0.0.1:54094 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54102 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54104 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54110 [200]: GET /bundles/nelmioapidoc/swagger-ui/swagger-ui-standalone-preset.js
[Wed May 28 12:35:14 2025] 127.0.0.1:54110 Closing
[Wed May 28 12:35:14 2025] 127.0.0.1:54118 [200]: GET /bundles/nelmioapidoc/init-swagger-ui.js
[Wed May 28 12:35:14 2025] 127.0.0.1:54118 Closing
